import { openai } from '@ai-sdk/openai';
import { Browserbase } from '@browserbasehq/sdk';
import { ObserveResult, Stagehand } from '@browserbasehq/stagehand';
import { CoreMessage, generateObject, generateText, LanguageModelV1, UserContent } from 'ai';
import 'dotenv/config';
import sharp from 'sharp';
import uWS from 'uWebSockets.js';
import { z } from 'zod';
import StagehandConfig from './stagehand.config.js';
import {
  FRAME_RATE,
  IMAGE_QUALITY,
  IMAGE_WIDTH,
  MAX_STEPS,
  WS_PING_INTERVAL,
} from './types/constant.js';
import { BrowserClientEvent, SocketUserData, Step } from './types/index.js';
import { getClosestRegion } from './utils/regionSelector.js';
import runStagehand from './utils/runStagehand.js';
import {
  sendAgentMessage,
  sendBrowserEnv,
  sendGoalSummary,
  sendLiveViewLink,
  sendSessionControl,
  sendStatus,
  sendSteps,
  sendUrl,
} from './utils/sendMessage.js';

import SocketUsers from './services/SocketUserPool.js';
import { handleClick, handleReload, handleScroll } from './utils/localBrowserControls.js';

const LLMClient = openai('gpt-4o');
const isLocalBrowser = process.env.STAGEHAND_ENV === 'LOCAL';
const PORT = process.env.WS_PORT || 3001;

const browserbaseClient = isLocalBrowser
  ? null
  : new Browserbase({ apiKey: process.env.BROWSERBASE_API_KEY! });

// Screenshot service with sharp optimization
const startScreenshotCapture = async (ws: uWS.WebSocket<SocketUserData>) => {
  const socketUser = SocketUsers.get(ws.getUserData().userId);
  const userStagehand = socketUser?.stagehand;
  if (!userStagehand || !userStagehand.page) {
    console.log('No active Stagehand instance for screenshot');
    return;
  }

  const captureScreenshot = async () => {
    try {
      if (!userStagehand || !userStagehand.page) {
        console.log('Stagehand instance lost, stopping screenshot capture');
        // stopScreenshotCapture(ws.getUserData().userId);
        SocketUsers.stopScreenshotCapture(ws.getUserData().userId);
        sendStatus(ws, 'Browser crashed - restart required');
        return;
      }

      // Capture screenshot with Stagehand
      const screenshot = await userStagehand.page.screenshot({
        type: 'jpeg',
        quality: 80,
        fullPage: false,
      });

      // Optimize with sharp
      const optimizedBuffer = await sharp(screenshot)
        .resize({ width: IMAGE_WIDTH })
        .jpeg({ quality: IMAGE_QUALITY, progressive: true })
        .toBuffer();

      ws.send(optimizedBuffer, true);

      // Schedule next screenshot
      socketUser.screenshotInterval = setTimeout(captureScreenshot, FRAME_RATE);
    } catch (error) {
      console.error('Error capturing screenshot:', error);
      // Continue capturing on image compression failure, just skip this frame
      if (
        error instanceof Error &&
        (error.message?.includes('sharp') || error.message?.includes('compression'))
      ) {
        console.log('Image compression failed, skipping frame');
        socketUser.screenshotInterval = setTimeout(captureScreenshot, FRAME_RATE);
      } else {
        // Other errors might indicate browser crash
        SocketUsers.stopScreenshotCapture(ws.getUserData().userId);
        sendStatus(ws, 'Screenshot capture failed - browser may have crashed');
      }
    }
  };

  // Start first capture
  captureScreenshot();
};

const buildPreviousStepsText = (previousSteps: Step[]) => {
  return previousSteps.length > 0
    ? `## Previous steps taken:
${previousSteps
  .map(
    (step, index) => `
Step ${index + 1}:
- Action: ${step.text}
- Reasoning: ${step.reasoning || ''}
- Tool Used: ${step.tool}
- Instruction: ${step.instruction}
`,
  )
  .join('\n')}`
    : '';
};

const summarizeGoal = async (params: {
  stagehand: Stagehand;
  goal: string;
  allSteps: Step[];
  isDone: boolean;
}) => {
  const { stagehand, goal, allSteps = [], isDone } = params;
  const currentUrl = stagehand.page.url();
  //   const currentPageTitle = stagehand.page.title();
  let currentPageTitle = '';
  try {
    currentPageTitle = await stagehand.page.title();
  } catch (error) {}
  const prompt = `You are an intelligent web browser automation agent.

## Current Context:
**Goal:** "${goal}"
**Current URL:** ${currentUrl || 'unknown'}
**Page Title:** ${currentPageTitle || 'unknown'}
${isDone ? 'You have achieved the goal.' : 'You have reached the maximum allowed number of steps without fully achieving the goal.'}

${buildPreviousStepsText(allSteps)}

## Your task: Read the provided goal, final page URL, page title and the list of steps taken, tell user what you have achieved so far. Write in plain, concise language that a non-technical user can understand. You may also have a screenshot of the current page. If a screenshot is provided, use it as an additional reference to verify the current state and describe relevant details.

## Output requirements:
- Return your answer in plain Markdown format.
- Start with one short opening sentence summarizing the overall outcome (plain text, no bullet, no markdown heading).
- After the opening sentence, provide details as bullet points (unordered or numbered lists).
- No headings, no icons, no emojis.
- Do not reveal sensitive information such as usernames, passwords, tokens, or any other information that could compromise the security of the system.
- Do not mention how you inferred the result (e.g., “as indicated by the URL and page title”) unless the goal explicitly requests that technical detail.
- Keep each bullet point concise (max 2 sentence).
- If a screenshot is provided: Include 2 - 3 bullet points describing what you see and what is visible that is relevant to the goal. Skip irrelevant UI details.
${
  !isDone
    ? `- Suggest **1 - 2 possible next actions** the user could take.
- End with a bullet point explicitly asking the user which action they want to proceed with.`
    : ''
}
`;

  const messageContent: UserContent = [
    {
      type: 'text',
      text: prompt,
    },
  ];

  if (
    (currentUrl && currentUrl !== 'about:blank') ||
    (allSteps.length > 0 && allSteps.some((step) => step.tool === 'GOTO'))
  ) {
    const screenshot = await stagehand.page.screenshot({
      type: 'jpeg',
      quality: 80,
      fullPage: false,
    });
    console.log('Summarize Goal screenshot');
    messageContent.push({
      type: 'image',
      image: screenshot,
    });
  }

  const message: CoreMessage = {
    role: 'user',
    content: messageContent,
  };

  const result = await generateText({
    model: LLMClient as LanguageModelV1,
    messages: [message],
    temperature: 0,
  });

  return result.text;
};

const determineNextStep = async ({
  stagehand,
  goal,
  previousSteps = [],
  previousExtraction,
}: {
  stagehand: Stagehand;
  goal: string;
  previousSteps?: Step[];
  previousExtraction?: string | ObserveResult[];
}) => {
  const currentUrl = stagehand.page.url();
  //   const currentPageTitle = stagehand.page.title();
  let currentPageTitle = '';
  try {
    currentPageTitle = await stagehand.page.title();
  } catch (error) {}

  //   console.log('determineNextStep ::: ', '\n', currentUrl, '\n', currentPageTitle);

  const firstContent = `You are an intelligent web browser automation agent. Your task is to determine the next single action to achieve the given goal.

## Current Context:
**Goal:** "${goal}"
**Current URL:** ${currentUrl || 'unknown'}
**Page Title:** ${currentPageTitle || 'unknown'}

${buildPreviousStepsText(previousSteps)}

## Task:
Analyze the current context, previouse steps (if any) and determine the immediate next step to take to achieve the goal. Each step must have these fields:
- text: string, required - a short, direct description of the action to take
- reasoning: string, required. The reasoning for taking this action. Keep reasoning short (1 - 2 sentences).
- tool: string, required.
- instruction: string, required - clear instruction to execute the action.

## Important guidelines:
1. Break down complex actions into individual atomic steps
2. Use only one action at a time, such as:
   - Single click on a specific element
   - Type into a single input field
   - Select a single option
3. Avoid combining multiple actions in one instruction
4. If multiple actions are needed, they should be separate steps
5. Do NOT speculate about future steps after the goal is achieved.
6. If the goal has been achieved, use tool="CLOSE"
`;

  const content: UserContent = [
    {
      type: 'text',
      text: firstContent,
    },
  ];

  // Add screenshot if navigated to a page previously
  if (
    (currentUrl && currentUrl !== 'about:blank') ||
    (previousSteps.length > 0 && previousSteps.some((step) => step.tool === 'GOTO'))
  ) {
    const screenshot = await stagehand.page.screenshot({
      type: 'jpeg',
      quality: 80,
      fullPage: false,
    });
    content.push({
      type: 'image',
      image: screenshot,
    });
  }

  if (previousExtraction) {
    content.push({
      type: 'text',
      text: `The result of the previous ${
        Array.isArray(previousExtraction) ? 'observation' : 'extraction'
      } is: ${previousExtraction}.`,
    });
  }

  const message: CoreMessage = {
    role: 'user',
    content,
  };

  const result = await generateObject({
    model: LLMClient as LanguageModelV1,
    schema: z.object({
      text: z.string(),
      reasoning: z.string(),
      tool: z.enum(['GOTO', 'ACT', 'EXTRACT', 'OBSERVE', 'CLOSE', 'WAIT', 'NAVBACK']),
      instruction: z.string(),
    }),
    messages: [message],
    temperature: 0,
  });

  return {
    result: result.object,
    steps: [...previousSteps, result.object],
  };
};

const checkAgentControlling = (ws: uWS.WebSocket<SocketUserData>) => {
  try {
    const sessionController = SocketUsers.get(ws.getUserData().userId)?.sessionController;
    return sessionController === 'agent';
  } catch (error) {}
  return false;
};

// TODO: refactor, try using while loop
const handleGoal = async (ws: uWS.WebSocket<SocketUserData>, goalEvent: BrowserClientEvent) => {
  if (goalEvent.type !== 'goal') return;
  const { action, goal, previousSteps, step, start } = goalEvent.data;
  const stagehand = SocketUsers.get(ws.getUserData().userId)!.stagehand;

  if (!stagehand || stagehand.isClosed) {
    // TODO: send error
    return;
  }
  if (start) {
    SocketUsers.get(ws.getUserData().userId)!.sessionController = 'agent';
    sendSessionControl(ws, 'agent');
  }
  if (!checkAgentControlling(ws)) {
    console.log('User is in control, skip goal');
    return;
  }
  switch (action) {
    case 'GET_NEXT_STEP': {
      let data = null;
      if (!checkAgentControlling(ws)) break;
      try {
        data = await determineNextStep({
          stagehand,
          goal,
          previousSteps,
        });
      } catch (error) {}
      if (!checkAgentControlling(ws)) break;

      if (data && data.steps.length < MAX_STEPS && data.result.tool !== 'CLOSE') {
        sendSteps(ws, {
          action,
          result: data.result,
          steps: data.steps,
          done: false,
        });
      } else {
        try {
          if (!checkAgentControlling(ws)) break;
          const summary = await summarizeGoal({
            stagehand,
            goal,
            allSteps: data?.steps || previousSteps,
            isDone: true,
          });
          if (!checkAgentControlling(ws)) break;
          sendGoalSummary(ws, summary);
          sendSessionControl(ws, 'user');
        } catch (error) {}
      }
      break;
    }
    case 'EXECUTE_STEP': {
      if (!step || !checkAgentControlling(ws)) return;

      try {
        await runStagehand({
          stagehand,
          method: step.tool,
          instruction: step.instruction,
        });
      } catch (error) {}

      if (!checkAgentControlling(ws)) break;

      sendSteps(ws, {
        action,
        result: step,
        steps: [],
        done: step.tool === 'CLOSE',
      });
      if (step.tool === 'CLOSE') {
        sendSessionControl(ws, 'user');
      }

      break;
    }
  }
};

// Initialize WebSocket server
const app = uWS
  .App()
  .ws<SocketUserData>('/ws', {
    idleTimeout: 0,
    // TODO: do we need this when set idleTimeout = 0 (??)
    sendPingsAutomatically: true,
    upgrade: (res, req, context) => {
      console.log('An Http connection wants to become WebSocket, URL: ' + req.getUrl() + '!!');
      const query = req.getQuery();
      const urlSearchParams = new URLSearchParams(query);
      let userData: SocketUserData = {
        userId: urlSearchParams.get('userId') || Date.now().toString(),
        userName: urlSearchParams.get('userName') || 'Anonymous',
      };

      /* This immediately calls open handler, you must not use res after this call */
      res.upgrade(
        userData,
        /* Spell these correctly */
        req.getHeader('sec-websocket-key'),
        req.getHeader('sec-websocket-protocol'),
        req.getHeader('sec-websocket-extensions'),
        context,
      );
    },
    open: async (ws) => {
      console.log('Client connected ', ws.getUserData().userId);

      SocketUsers.add(ws.getUserData().userId, {
        userId: ws.getUserData().userId,
        socket: ws,
        stagehand: null,
        screenshotInterval: null,
        pingInterval: setInterval(() => {
          if (ws?.getUserData().userId) {
            try {
              ws.ping(JSON.stringify({ type: 'ping', userId: ws.getUserData().userId }));
            } catch (error) {}
          }
        }, WS_PING_INTERVAL),
        sessionController: 'user',
      });

      const userData = ws.getUserData();

      // Send connection confirmation
      ws.send('Connected to Browser');

      // Send current status
      const userStagehand = SocketUsers.get(userData.userId)?.stagehand;
      const status = !!userStagehand ? 'Browser active' : 'No browser instance';
      ws.send(
        JSON.stringify({
          type: 'status',
          message: status,
          timestamp: new Date().toISOString(),
        }),
      );
    },

    message: async (ws: uWS.WebSocket<SocketUserData>, message: ArrayBuffer, isBinary: boolean) => {
      if (isBinary) return;
      try {
        const browserClientEvent: BrowserClientEvent = JSON.parse(Buffer.from(message).toString());
        let userStagehand = SocketUsers.get(ws.getUserData().userId)?.stagehand;
        let isReconnecting = false;

        // User provide url and
        if (browserClientEvent.type === 'start_browser') {
          console.log('START BROWSER ::: ', browserClientEvent, '\n');
          let useLocalBrowser = isLocalBrowser;

          if (!userStagehand) {
            try {
              sendStatus(ws, 'Initializing browser...', 'initializing');

              // @ts-ignore
              const initStagehand = new Stagehand({
                ...StagehandConfig,
                ...(isLocalBrowser
                  ? {}
                  : {
                      browserbaseSessionCreateParams: {
                        ...StagehandConfig.browserbaseSessionCreateParams,
                        region: getClosestRegion(browserClientEvent.data?.timezone),
                      },
                    }),
              });
              await initStagehand.init();

              SocketUsers.get(ws.getUserData().userId)!.stagehand = initStagehand;
              userStagehand = initStagehand;

              SocketUsers.get(ws.getUserData().userId)!.env = process.env.STAGEHAND_ENV as
                | 'LOCAL'
                | 'BROWSERBASE';
              sendBrowserEnv(ws, process.env.STAGEHAND_ENV!);
            } catch (error) {
              console.error('Failed to initialize:', error);
              sendStatus(
                ws,
                'Failed to initialize: ' +
                  (error instanceof Error ? error.message : 'Unknown error'),
              );
            }
          }
          // Fallback to local browser if browserbase failed
          if (!isLocalBrowser && !userStagehand) {
            try {
              const initStagehand = new Stagehand({
                ...StagehandConfig,
                env: 'LOCAL',
              });
              await initStagehand.init();

              SocketUsers.get(ws.getUserData().userId)!.stagehand = initStagehand;
              userStagehand = initStagehand;

              useLocalBrowser = true;
              SocketUsers.get(ws.getUserData().userId)!.env = 'LOCAL';
              sendBrowserEnv(ws, 'LOCAL');
            } catch (error) {
              sendStatus(
                ws,
                'Failed to initialize local browser (fallback): ' +
                  (error instanceof Error ? error.message : 'Unknown error'),
              );
            }
          }
          const navigatingUrl = browserClientEvent.data.url || 'https://google.com';
          const navigatingStep: Step = {
            text: `Navigating to ${navigatingUrl}`,
            reasoning: `Navigating to ${navigatingUrl}`,
            tool: 'GOTO',
            instruction: navigatingUrl,
          };
          if (userStagehand) {
            await userStagehand.page.goto(navigatingUrl, { waitUntil: 'commit', timeout: 60000 });
            if (browserbaseClient && userStagehand.browserbaseSessionID) {
              const liveViewLinks = await browserbaseClient.sessions.debug(
                userStagehand.browserbaseSessionID!,
              );
              sendLiveViewLink(ws, liveViewLinks.debuggerFullscreenUrl);
            }
            sendUrl(ws, userStagehand.page.url());
            sendSteps(ws, {
              action: 'START',
              result: navigatingStep,
              steps: [navigatingStep],
              done: false,
            });
            sendStatus(ws, 'Running...', 'running');

            if (!browserbaseClient || useLocalBrowser) {
              // Start screenshot capture
              startScreenshotCapture(ws);
            }
          } else {
            sendSteps(ws, {
              action: 'START',
              result: navigatingStep,
              steps: [navigatingStep],
              done: true,
              error: 'Unknown error',
            });
          }
          return;
        }

        if (browserClientEvent.type === 'reconnect_browser') {
          console.log(
            'Check stagehand when reconnect to browser ::: ',
            '\n',
            new Date().toISOString(),
            '\n',
            'isReconnecting ::: ',
            isReconnecting,
            '\n',
            'isLocalBrowser ::: ',
            isLocalBrowser,
            '\n',
            'browserbaseSessionID ::: ',
            userStagehand?.browserbaseSessionID,
            '\n',
            'stagehand.isClosed ::: ',
            userStagehand?.isClosed,
          );
          if (
            isReconnecting ||
            isLocalBrowser ||
            !browserbaseClient ||
            !userStagehand ||
            !userStagehand.browserbaseSessionID ||
            userStagehand.isClosed
          ) {
            return;
          }

          //   sendStatus(ws, 'Reconnecting...', 'reconnecting');
          sendStatus(ws, 'Reconnecting...', 'initializing');

          const currentBrowserbaseSessionId = userStagehand.browserbaseSessionID;
          let currentURL = '';
          try {
            currentURL = userStagehand.page.url();
          } catch (error) {}
          SocketUsers.get(ws.getUserData().userId)!.stagehand = null;
          try {
            await userStagehand.close();
          } catch (error) {}
          userStagehand = null;
          try {
            // @ts-ignore
            userStagehand = new Stagehand({
              ...StagehandConfig,
              ...(isLocalBrowser
                ? {}
                : {
                    browserbaseSessionCreateParams: {
                      ...StagehandConfig.browserbaseSessionCreateParams,
                      region: getClosestRegion(browserClientEvent.data?.timezone),
                    },
                  }),
              browserbaseSessionID: currentBrowserbaseSessionId,
            });
            await userStagehand.init();
          } catch (error) {
            userStagehand = null;
          }

          try {
            if (!userStagehand) {
              // @ts-ignore
              userStagehand = new Stagehand({
                ...StagehandConfig,
                ...(isLocalBrowser
                  ? {}
                  : {
                      browserbaseSessionCreateParams: {
                        ...StagehandConfig.browserbaseSessionCreateParams,
                        region: getClosestRegion(browserClientEvent.data?.timezone),
                      },
                    }),
              });
              await userStagehand.init();
            }
          } catch (error) {
            userStagehand = null;
          }
          if (userStagehand) {
            await userStagehand.page.goto(currentURL || 'https://google.com', {
              waitUntil: 'commit',
              timeout: 60000,
            });
            if (browserbaseClient && userStagehand.browserbaseSessionID) {
              const liveViewLinks = await browserbaseClient.sessions.debug(
                userStagehand.browserbaseSessionID!,
              );
              const liveViewLink = liveViewLinks.debuggerFullscreenUrl;
              sendLiveViewLink(ws, liveViewLink);
            }
            sendUrl(ws, userStagehand.page.url());
            sendStatus(ws, 'Reconnected', 'running');
          } else {
            sendStatus(ws, 'Failed to reconnect', 'reconnect_failed');
          }
          SocketUsers.get(ws.getUserData().userId)!.stagehand = userStagehand;

          return;
        }

        if (browserClientEvent.type === 'agent_instruction') {
          if (!userStagehand) {
            sendStatus(ws, 'No active browser instance');
            return;
          }
          const agent = userStagehand.agent({
            provider: 'openai',
            model: 'computer-use-preview',
          });

          const result = await agent.execute({
            instruction: browserClientEvent.data.instruction,
            autoScreenshot: false,
          });

          if (result.message) {
            sendAgentMessage(ws, result.message);
          }

          //   console.log('instruction agent result ::: ', result);
          return;
        }

        if (browserClientEvent.type === 'goal') {
          try {
            handleGoal(ws, browserClientEvent);
          } catch (error) {}
          return;
        }

        if (browserClientEvent.type === 'test_onboarding_task') {
          // Navigate to the URL
          const stagehand = SocketUsers.get(ws.getUserData().userId)!.stagehand;
          if (!stagehand || !stagehand.page) {
            return;
          }
          const currentPage = stagehand.page;
          await currentPage.goto('https://sandbox-app.aimyone.com', {
            waitUntil: 'commit',
            timeout: 60000,
          });
          await Promise.all([
            currentPage.act('enter username: <EMAIL>'),
            currentPage.act('enter password: 9jg@tD5nwLUkgU'),
          ]);
          await currentPage.act('click Login');
          await currentPage.waitForLoadState('domcontentloaded');
          const [action1] = await currentPage.observe('Click Bookings dropdown menu');
          await currentPage.act(action1);
          const [action2] = await currentPage.observe('Choose New Booking menu');
          await currentPage.act(action2);
        }

        if (browserClientEvent.type === 'session_control') {
          try {
            SocketUsers.get(ws.getUserData().userId)!.sessionController =
              browserClientEvent.data.controller;
            sendSessionControl(ws, browserClientEvent.data.controller);
          } catch (error) {}
          return;
        }

        if (browserClientEvent.type === 'click') {
          await handleClick(ws, browserClientEvent.data.x, browserClientEvent.data.y);
          return;
        }

        if (browserClientEvent.type === 'reload') {
          await handleReload(ws);
          return;
        }

        if (browserClientEvent.type === 'scroll') {
          await handleScroll(ws, browserClientEvent.data.direction);
          return;
        }
      } catch (error) {
        console.error('Message parsing error:', error);
      }
    },

    ping: (ws, _message) => {
      console.log(
        '🏓 Received ping from client ',
        ws.getUserData().userId,
        new Date().toISOString(),
      );
    },

    pong: (ws, _message) => {
      console.log(
        '🏓 Received pong from client ',
        ws.getUserData().userId,
        new Date().toISOString(),
      );
    },

    close: async (ws: uWS.WebSocket<SocketUserData>, code: number, message: ArrayBuffer) => {
      try {
        console.log(
          'Client disconnected ',
          ws.getUserData().userId,
          ' with code ',
          code,
          ' and message ',
          Buffer.from(message).toString(),
        );
      } catch (error) {
        console.error('Client disconnected !!!');
      }
      // TODO: How to reconnect?
      try {
        const { browserbaseSessionId } = SocketUsers.remove(ws.getUserData().userId);
        if (browserbaseClient && browserbaseSessionId) {
          await browserbaseClient?.sessions.update(browserbaseSessionId, {
            status: 'REQUEST_RELEASE',
            projectId: process.env.BROWSERBASE_PROJECT_ID || '',
          });
        }
      } catch (error) {}
    },
  })
  .any('/health', (res: uWS.HttpResponse, req: uWS.HttpRequest) => {
    res.writeHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({ status: 'OK', timestamp: new Date().toISOString() }));
  })
  .any('/*', (res: uWS.HttpResponse, req: uWS.HttpRequest) => {
    res.end('WebSocket server running on /ws');
  })
  .listen(Number(PORT), (token: any) => {
    if (token) {
      console.log(`🚀 uWebSockets.js server running on ws://localhost:${PORT}/ws`);
      console.log(`🚀 Using ${process.env.STAGEHAND_ENV} browser`);
    } else {
      console.error('Failed to listen to port', PORT);
    }
  });

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down...');

  SocketUsers.destroy(browserbaseClient);

  process.exit(0);
});

export default app;
