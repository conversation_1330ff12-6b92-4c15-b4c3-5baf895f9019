import { Stagehand } from '@browserbasehq/stagehand';
import uWS from 'uWebSockets.js';

interface SocketUserData {
  userId: string;
  userName: string;
}

interface PoolSocketUser {
  userId: string;
  socket: uWS.WebSocket<SocketUserData>;
  stagehand: Stagehand | null;
  screenshotInterval: NodeJS.Timeout | null;
  pingInterval: NodeJS.Timeout | null;
  sessionController?: 'user' | 'agent';
  env?: 'LOCAL' | 'BROWSERBASE';
}

interface Step {
  text: string;
  reasoning: string;
  tool: 'GOTO' | 'ACT' | 'EXTRACT' | 'OBSERVE' | 'CLOSE' | 'WAIT' | 'NAVBACK';
  instruction: string;
}

interface BaseBrowserEvent {
  type: string;
  data?: Record<string, any>;
}

interface BrowserClickEvent extends BaseBrowserEvent {
  type: 'click';
  data: {
    x: number;
    y: number;
  };
}

interface BrowserReloadEvent extends BaseBrowserEvent {
  type: 'reload';
  data: {
    x: number;
    y: number;
  };
}

interface BrowserScrollYEvent extends BaseBrowserEvent {
  type: 'scroll';
  data: {
    direction: 'up' | 'down';
  };
}

interface BrowserStartEvent extends BaseBrowserEvent {
  type: 'start_browser';
  data: {
    url?: string;
    timezone?: string;
  };
}

interface BrowserReconnectEvent extends BaseBrowserEvent {
  type: 'reconnect_browser';
}

interface BrowserCloseEvent extends BaseBrowserEvent {
  type: 'close_browser';
}

interface BrowserStartDemoEvent extends BaseBrowserEvent {
  type: 'start_demo';
}

interface BrowserInstructionEvent extends BaseBrowserEvent {
  type: 'agent_instruction';
  data: {
    instruction: string;
  };
}

interface BrowserGoalEvent extends BaseBrowserEvent {
  type: 'goal';
  data: {
    goal: string;
    action: 'START' | 'GET_NEXT_STEP' | 'EXECUTE_STEP';
    previousSteps: Step[];
    start?: boolean;
    step?: Step;
  };
}

interface BrowserSessionControlEvent extends BaseBrowserEvent {
  type: 'session_control';
  data: {
    controller: 'user' | 'agent';
  };
}

interface BrowserTestOnboardingTaskEvent extends BaseBrowserEvent {
  type: 'test_onboarding_task';
}

type BrowserClientEvent =
  | BrowserClickEvent
  | BrowserStartEvent
  | BrowserStartDemoEvent
  | BrowserInstructionEvent
  | BrowserCloseEvent
  | BrowserReloadEvent
  | BrowserScrollYEvent
  | BrowserGoalEvent
  | BrowserReconnectEvent
  | BrowserSessionControlEvent
  | BrowserTestOnboardingTaskEvent;

export { BrowserClientEvent, PoolSocketUser, SocketUserData, Step };

