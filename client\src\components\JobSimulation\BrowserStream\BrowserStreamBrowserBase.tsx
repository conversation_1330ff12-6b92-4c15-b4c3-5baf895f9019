import { memo, useState } from 'react';
import { useRecoilValue } from 'recoil';
import store from '~/store';
import { cn } from '~/utils';
import ConfirmTakeControlDialog from './ConfirmTakeControlDialog';

interface BrowserStreamBrowserBaseProps {
  liveViewLink: string;
  // remoteBrowserRef: React.RefObject<HTMLIFrameElement>;
  handleTakeControl: () => void;
  sendTestOnboardingTask: () => void;
}

const BrowserStreamBrowserBase = ({
  liveViewLink,
  // remoteBrowserRef,
  handleTakeControl,
  sendTestOnboardingTask,
}: BrowserStreamBrowserBaseProps) => {
  const [isOpenTakeControlDialog, setIsOpenTakeControlDialog] = useState(false);
  const jobSimulationBrowserData = useRecoilValue(store.jobSimulationBrowserData);

  const confirmTakeControl = () => {
    handleTakeControl();
    setIsOpenTakeControlDialog(false);
  };

  return (
    <>
      <iframe
        src={liveViewLink}
        // ref={remoteBrowserRef}
        sandbox="allow-same-origin allow-scripts"
        allow="clipboard-read; clipboard-write"
        // className={cn('pointer-events-none h-full w-full select-none')}
        className={cn(
          'h-full w-full',
          jobSimulationBrowserData.controller === 'agent' ? 'pointer-events-none select-none' : '',
        )}
      />

      <button
        className="absolute left-0 bottom-20"
        onClick={() => {
          console.log('sendTestOnboardingTask');
          sendTestOnboardingTask?.();
        }}
      >
        Test Onboarding Task
      </button>

      {jobSimulationBrowserData.controller === 'agent' && (
        <>
          <div
            className="absolute inset-0 cursor-pointer bg-transparent"
            onClick={() => setIsOpenTakeControlDialog(true)}
          ></div>
          <ConfirmTakeControlDialog
            confirmTakeControl={confirmTakeControl}
            isOpen={isOpenTakeControlDialog}
            setIsOpen={setIsOpenTakeControlDialog}
          />
        </>
      )}
    </>
  );
};

export default memo(BrowserStreamBrowserBase);
