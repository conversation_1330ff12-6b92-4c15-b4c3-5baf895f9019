import React, { memo, useEffect, useRef, useState } from 'react';
import { useRecoilState } from 'recoil';
import store from '~/store';
import BrowserStreamBrowserBase from './BrowserStreamBrowserBase';
import BrowserStreamCanvas from './BrowserStreamCanvas';

interface BrowserStreamProps {
  wsRef: React.MutableRefObject<WebSocket | null>;
  wsUrl: string;
  browserUrl?: string;
  onReceiveAgentMessage?: (msg: string) => void;
  onReceiveStatusCode?: (code: string) => void;
}
const maxRetries = 3;

const BrowserStream: React.FC<BrowserStreamProps> = ({
  wsRef,
  wsUrl,
  browserUrl,
  onReceiveAgentMessage,
  onReceiveStatusCode,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // const remoteBrowserRef = useRef<HTMLIFrameElement>(null);
  const goalState = useRef<{ goal?: string; steps?: any[]; done?: boolean } | null>(null);
  const [status, setStatus] = useState<string>('Connecting...');
  const [statusCode, setStatusCode] = useState<string>('');
  const [browserEnv, setBrowserEnv] = useState<'LOCAL' | 'BROWSERBASE' | ''>('');
  const [_connected, setConnected] = useState(false);
  const [showReconnectDialog, setShowReconnectDialog] = useState(false);
  const [initUrl] = useState<string>(browserUrl || 'https://google.com');
  const [hasSentInitUrl, setHasSentInitUrl] = useState(false);
  const [liveViewLink, setLiveViewLink] = useState<string>('');
  const [currentUrl, setCurrentUrl] = useState<string>('');

  const [jobSimulationBrowserData, setJobSimulationBrowserData] = useRecoilState(
    store.jobSimulationBrowserData,
  );

  const sendWsData = (obj: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      if (obj instanceof String) {
        wsRef.current.send(obj as string);
      } else {
        try {
          wsRef.current.send(JSON.stringify(obj));
        } catch (error) {
          console.error('Error sending data to ws server : ', error);
        }
      }
    }
  };

  const handleTakeControl = () => {
    // TODO: When user take control while agent is in the middle of a task, insert a resume button. Do not clear goalState.
    sendWsData({
      type: 'session_control',
      data: {
        controller: 'user',
      },
    });
  };

  useEffect(() => {
    console.log('Browser Agent ::: handle WS connect / close ', wsUrl, !!wsRef.current);
    if (wsRef.current) return;
    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Browser Agent ::: Connect to server');
        setStatus('Connected to server');
        setConnected(true);
        setShowReconnectDialog(false);
      };

      wsRef.current.onmessage = (event: MessageEvent) => {
        // Handle incoming data
        if (typeof event.data === 'string') {
          try {
            const serverEvent = JSON.parse(event.data);
            const serverEventType = serverEvent.type;
            const serverEventData = serverEvent.data;
            if (serverEventType === 'status') {
              setStatus(serverEventData.message);
              if (serverEventData.code !== undefined) {
                setStatusCode(serverEventData.code);
                onReceiveStatusCode?.(serverEventData.code);
              }

              return;
            }
            if (serverEventType === 'url') {
              setCurrentUrl(serverEventData.url);

              return;
            }
            if (serverEventType === 'agent_message') {
              onReceiveAgentMessage?.(serverEventData.message);
              return;
            }
            if (serverEventType === 'live_view_link' && serverEventData.url) {
              setLiveViewLink(serverEventData.url);
              return;
            }
            if (serverEventType === 'browser_env' && serverEventData.env) {
              setBrowserEnv(serverEventData.env);
              return;
            }

            if (serverEventType === 'goal_step') {
              const { action, result, steps, done, error } = serverEventData;

              if (action === 'START') {
                goalState.current = {
                  goal: '',
                  steps,
                  done: false,
                };
              }

              if (error) {
                // TODO: Handle error
                return;
              }

              if (action === 'GET_NEXT_STEP') {
                if (!!result?.reasoning) {
                  onReceiveAgentMessage?.(result.reasoning);
                }
                if (done) {
                  console.log('GET_NEXT_STEP ::: ALL DONE');
                  // alert("ALL DONE");
                  goalState.current = null;
                } else {
                  goalState.current = {
                    goal: goalState.current?.goal || '',
                    steps,
                    done: false,
                  };

                  wsRef.current?.send(
                    JSON.stringify({
                      type: 'goal',
                      data: {
                        action: 'EXECUTE_STEP',
                        goal: goalState.current?.goal || '',
                        previousSteps: goalState.current?.steps || [],
                        step: result,
                      },
                    }),
                  );
                }

                return;
              }
              if (action === 'EXECUTE_STEP') {
                // TODO: Check DONE --> Unlock chat
                // Else call GET_NEXT_STEP
                if (done) {
                  console.log('EXECUTE_STEP ::: ALL DONE');
                  // alert("ALL DONE");
                  goalState.current = null;
                } else {
                  wsRef.current?.send(
                    JSON.stringify({
                      type: 'goal',
                      data: {
                        action: 'GET_NEXT_STEP',
                        goal: goalState.current?.goal || '',
                        previousSteps: goalState.current?.steps || [],
                      },
                    }),
                  );
                }
                return;
              }
            }

            if (serverEventType === 'goal_summary') {
              const { summary = '' } = serverEventData;
              onReceiveAgentMessage?.(summary);
              goalState.current = null;

              return;
            }

            if (serverEventType === 'session_control') {
              console.log('receive session_control ::: ', serverEventData);
              // if (!remoteBrowserRef.current) return;

              // remoteBrowserRef.current.style.pointerEvents =
              //   serverEventData.controller === 'agent' ? 'none' : 'auto';
              // remoteBrowserRef.current.style.userSelect =
              //   serverEventData.controller === 'agent' ? 'none' : 'auto';

              setJobSimulationBrowserData((prev) => ({
                ...prev,
                controller: serverEventData.controller,
                ...(serverEventData.controller === 'user' ? { goal: '' } : {}),
              }));
              // TODO: Do we need to handle pause / resume? How to handle pause / resume (??)
              if (serverEventData.controller === 'user') {
                goalState.current = null;
              }
              return;
            }
          } catch {
            // Plain text status message
            setStatus(event.data);
          }
          return;
        }

        // Handle binary image data
        if (event.data instanceof Blob) {
          const img = new Image();
          img.onload = () => {
            const canvas = canvasRef.current;
            // const overlayCanvas = overlayCanvasRef.current;

            if (canvas) {
              const ctx = canvas.getContext('2d');
              if (ctx) {
                // Update canvas size to match image
                canvas.width = img.width;
                canvas.height = img.height;
                // overlayCanvas.width = img.width;
                // overlayCanvas.height = img.height;

                // Draw the image
                ctx.drawImage(img, 0, 0);
              }
            }

            // Clean up blob URL
            URL.revokeObjectURL(img.src);
          };
          img.src = URL.createObjectURL(event.data);
        }
      };

      wsRef.current.onerror = (e) => {
        console.log('Browser Agent ::: Error');
        setStatus('Error connecting to server');
        goalState.current = null;
        setConnected(false);
      };

      wsRef.current.onclose = (event: CloseEvent) => {
        console.log('Browser Agent ::: Closing ... ::: ', event);
        setLiveViewLink('');
        setConnected(false);
        goalState.current = null;
      };
    } catch (error) {
      console.error('Browser Agent ::: WebSocket connection error:', error);
      setStatus('Error creating WebSocket connection');
      setConnected(false);
      setShowReconnectDialog(true);
    }

    // Cleanup
    return () => {
      console.log('Browser Agent ::: Cleanup WebSocket connection');
      wsRef.current?.close();
      wsRef.current = null;
    };
  }, [wsUrl]);

  useEffect(() => {
    if (
      !jobSimulationBrowserData?.goal ||
      wsRef.current?.readyState !== WebSocket.OPEN ||
      // TODO: remove this logic, so user can send another goal
      // goalState.current?.done ||
      // TODO: update condition (??)
      jobSimulationBrowserData.goal === goalState.current?.goal ||
      statusCode !== 'running'
    ) {
      return;
    }

    goalState.current = {
      goal: jobSimulationBrowserData.goal,
      steps: [...(goalState.current?.steps || [])],
      done: false,
    };

    // sendWsData({
    //   type: 'session_control',
    //   data: {
    //     controller: 'agent',
    //   },
    // });

    sendWsData({
      type: 'goal',
      data: {
        action: 'GET_NEXT_STEP',
        start: true,
        goal: jobSimulationBrowserData.goal,
        previousSteps: goalState.current?.steps || [],
      },
    });
  }, [jobSimulationBrowserData?.goal, statusCode, wsRef.current]);

  useEffect(() => {
    if (initUrl && wsRef.current?.readyState === WebSocket.OPEN && !hasSentInitUrl) {
      setHasSentInitUrl(true);
      console.log('Browser Agent ::: send initUrl ::: ', initUrl);
      sendWsData({
        type: 'start_browser',
        data: {
          url: initUrl,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        },
      });
    }
  }, [initUrl, hasSentInitUrl, wsRef.current?.readyState]);

  useEffect(() => {
    function handleMessage(event: any) {
      if (
        event.data === 'browserbase-disconnected' &&
        wsRef.current?.readyState === WebSocket.OPEN
      ) {
        sendWsData({
          type: 'reconnect_browser',
        });
      }
    }

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleSendTestOnboardingTask = () => {
    sendWsData({
      type: 'test_onboarding_task',
    });
  };

  useEffect(() => {
    if (!statusCode) return;
    setJobSimulationBrowserData((prev) => ({
      ...prev,
      isSessionRunning: statusCode === 'running',
    }));
  }, [statusCode]);

  useEffect(() => {
    if (!jobSimulationBrowserData.shouldTakeControl) return;
    setJobSimulationBrowserData((prev) => ({
      ...prev,
      shouldTakeControl: false,
    }));
    handleTakeControl();
  }, [jobSimulationBrowserData.shouldTakeControl]);

  return (
    <div className="browser-stream flex h-full min-h-screen w-full flex-col">
      {/* Reconnect Dialog */}
      {showReconnectDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h3 className="mb-4 text-lg font-bold text-red-600">Connection Failed</h3>
            <p className="mb-4 text-gray-700">
              Failed to connect to the WebSocket server after {maxRetries} attempts. Please check if
              the server is running and try again.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => window.location.reload()}
                className="rounded bg-gray-500 px-4 py-2 text-white transition-colors hover:bg-gray-600"
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      )}
      {statusCode === 'initializing' && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <span className="text-gray-500">Initializing...</span>
        </div>
      )}

      {statusCode === 'running' && (
        <>
          {browserEnv === 'BROWSERBASE' && liveViewLink && (
            <BrowserStreamBrowserBase
              liveViewLink={liveViewLink}
              // remoteBrowserRef={remoteBrowserRef}
              handleTakeControl={handleTakeControl}
              sendTestOnboardingTask={handleSendTestOnboardingTask}
            />
          )}
          {browserEnv === 'LOCAL' && (
            <BrowserStreamCanvas
              status={status}
              currentUrl={currentUrl}
              canvasRef={canvasRef}
              sendWsData={sendWsData}
            />
          )}
        </>
      )}
    </div>
  );
};

export default memo(BrowserStream);
